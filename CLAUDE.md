# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Chinese-language math video generation system that creates educational videos with AI-generated voiceovers for children's math problems. The system follows an audio-first approach where voiceover timing drives the video animation synchronization.

**Core Workflow:**
1. Analyze math problems and generate step-by-step teaching scripts
2. Convert scripts to AI-generated speech audio 
3. Generate Manim animations synchronized to audio timing
4. Combine audio and video into final educational content

## Development Commands

### Running the Application
```bash
# Run the main application with default math problem
python main.py

# Run test version (contains more comprehensive examples)
python test.py
```

### Dependencies
- Python 3.12.8
- Manim (for mathematical animations)
- OpenAI API (for text-to-speech via `openai_text_to_speech`)
- Qwen/Kimi LLM APIs (for content generation)
- moviepy (for video processing)

### External Requirements
- `manim` command must be available in PATH
- Valid API keys configured in `configs/constant.py`

## Code Architecture

### Core Components

**Main Entry Points:**
- `main.py` - Simple entry point with default math problem
- `test.py` - Extended version with AudioFirstLessonCreator workflow

**Audio Generation Pipeline (`models/audios/`):**
- `voice_generator.py` - Main coordinator for script generation and TTS
- `text_to_speech.py` - OpenAI TTS integration  
- `video_syncer.py` - Syncs generated audio with video content

**Video Generation (`models/manim_video/`):**
- `math_lesson/math_lesson.py` - AdaptiveMathLesson scene class
- Uses audio duration data to time Manim animations precisely

**LLM Integration (`models/llms/`):**
- `llm.py` - Handles chat completions with retry logic and fallback
- Supports primary LLM config with QWEN fallback

**Content Generation (`models/prompts/`):**
- `generate_video_script.py` - Prompts for creating teaching scripts
- `generate_manim_video.py` - Prompts for generating Manim animation code

### Key Data Flow

1. **VoiceGenerator.generate_scripts()** - Creates structured teaching content using LLM
2. **VoiceGenerator.text_to_speech()** - Converts scripts to audio files and measures durations  
3. **VoiceGenerator.generate_lesson_script()** - Generates Manim Python code using audio timing
4. **AudioFirstLessonCreator.create_complete_lesson()** - Orchestrates the complete pipeline

### Configuration

- API keys and model configs in `configs/constant.py`
- Generated content stored as `scripts.json`
- Audio files saved as `audio_*.wav`
- Output videos in `output/` directory
- Manim renders to `media/videos/` subdirectories

### Important Files

- `scripts.json` - Contains generated teaching script structure
- `models/types/types.py` - VideoScript data structure definitions
- `models/utils.py` - Helper functions for message formatting and code extraction

### Testing

No formal test framework detected. The application includes sample math problems in Chinese for testing the complete pipeline.