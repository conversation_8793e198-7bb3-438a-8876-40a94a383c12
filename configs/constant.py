CONTENT = "content"
ROLE = 'role'
USER = 'user'
SYSTEM = 'system'
LANGUAGE = 'language'
IS_REASONING = 'is_reasoning'
ASSISTANT = 'assistant'
VIDEO_CLIP = '视频片段'
MAX_HISTORY_RUN = 10
CLAUDE_MAX_LENGTH = 128000
QWEN_PLUS_MODEL = 'qwen-plus-2025-07-14'
QWEN_CODER_LENGTH = 128000
MAX_LENGTH = 'max_length'
QWEN_BASE_URL: str = 'https://dashscope.aliyuncs.com/compatible-mode/v1'
QWEN_API_KEY: str = 'sk-43b66b8a677d4f628b2d7ecf7591f48a'
BASE_URL_NAME = 'server_url'
API_KEY_NAME = 'api_key'
MODEL_NAME_EN = 'model'
NAME = "name"
QWEN_PLUS_LLM_MODEL_DATA = {
    BASE_URL_NAME: QWEN_BASE_URL,
    API_KEY_NAME: QWEN_API_KEY,
    NAME: QWEN_PLUS_MODEL,
    MAX_LENGTH: QWEN_CODER_LENGTH,
    IS_REASONING: False
}
KIMI_K2_MODEL = 'kimi-k2-0711-preview'
KIMI_K2_LENGTH = 1280000
KIMI_BASE_URL: str = 'https://api.moonshot.cn/v1'
KIMI_API_KEY: str = 'sk-Slqmu4LOGpBhboHDfbrZy9IzqUqHZKkhddxtKll0u5rhfm3X'

LLM_DATA_KIMI_DATA = {
    MAX_LENGTH: KIMI_K2_LENGTH,
    NAME: KIMI_K2_MODEL,
    BASE_URL_NAME: KIMI_BASE_URL,
    API_KEY_NAME: KIMI_API_KEY,
    IS_REASONING: False
}
CLAUDE_MODEL = "claude-sonnet-4-20250514"
API_KEY_302 = 'sk-SHTS8kQyHCbuRVQbHuSV4TIX636tDGPat3ZjZCNC0HGrWatb'
BASE_URL_302 = 'https://api.302.ai/v1'

LLM_DATA_CLAUDE_DATA = {
    MAX_LENGTH: KIMI_K2_LENGTH,
    NAME: CLAUDE_MODEL,
    BASE_URL_NAME: BASE_URL_302,
    API_KEY_NAME: API_KEY_302,
    IS_REASONING: True

}
