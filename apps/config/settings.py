"""Application settings and configuration."""

import os
from dataclasses import dataclass
from typing import Optional


@dataclass
class LLMConfig:
    """LLM configuration settings."""
    api_key: str
    model_name: str = "qwen-turbo"
    max_tokens: int = 4000
    temperature: float = 0.7
    fallback_model: str = "qwen-plus"


@dataclass
class AudioConfig:
    """Audio generation configuration."""
    openai_api_key: str
    voice: str = "alloy"
    speed: float = 1.0
    output_format: str = "wav"


@dataclass
class VideoConfig:
    """Video generation configuration."""
    resolution: str = "480p15"
    output_dir: str = "output"
    temp_dir: str = "media"


@dataclass
class AppSettings:
    """Main application settings."""
    llm: LLMConfig
    audio: AudioConfig
    video: VideoConfig
    debug: bool = False
    
    @classmethod
    def from_env(cls) -> 'AppSettings':
        """Create settings from environment variables."""
        return cls(
            llm=LLMConfig(
                api_key=os.getenv("LLM_API_KEY", ""),
                model_name=os.getenv("LLM_MODEL", "qwen-turbo"),
                max_tokens=int(os.getenv("LLM_MAX_TOKENS", "4000")),
                temperature=float(os.getenv("LLM_TEMPERATURE", "0.7")),
                fallback_model=os.getenv("LLM_FALLBACK_MODEL", "qwen-plus")
            ),
            audio=AudioConfig(
                openai_api_key=os.getenv("OPENAI_API_KEY", ""),
                voice=os.getenv("AUDIO_VOICE", "alloy"),
                speed=float(os.getenv("AUDIO_SPEED", "1.0")),
                output_format=os.getenv("AUDIO_FORMAT", "wav")
            ),
            video=VideoConfig(
                resolution=os.getenv("VIDEO_RESOLUTION", "480p15"),
                output_dir=os.getenv("OUTPUT_DIR", "output"),
                temp_dir=os.getenv("TEMP_DIR", "media")
            ),
            debug=os.getenv("DEBUG", "false").lower() == "true"
        )