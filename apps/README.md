# Math Video Generation App - Clean Architecture

This directory contains a refactored version of the math video generation system with a clean, organized architecture.

## Architecture Overview

```
apps/
├── main.py                 # Application entry point
├── config/                 # Configuration management
│   ├── __init__.py
│   └── settings.py         # Application settings and config classes
├── core/                   # Domain models and business logic
│   ├── __init__.py
│   ├── models.py           # Core data models (VideoScript, AudioData, etc.)
│   └── exceptions.py       # Custom exception classes
├── services/               # Business logic services
│   ├── __init__.py
│   ├── llm_service.py      # LLM integration service
│   ├── audio_service.py    # Audio generation service
│   ├── video_service.py    # Video generation service
│   └── script_service.py   # Script management service
└── utils/                  # Utility functions and helpers
    ├── __init__.py
    ├── logger.py           # Logging utilities
    └── helpers.py          # General helper functions
```

## Key Features

### Clean Architecture Principles
- **Separation of Concerns**: Each layer has a single responsibility
- **Dependency Injection**: Services are injected rather than created internally
- **Configuration Management**: Centralized configuration with environment variable support
- **Error Handling**: Structured exception handling with custom exception types
- **Logging**: Comprehensive logging throughout the application

### Core Components

#### 1. Configuration Layer (`config/`)
- `AppSettings`: Main configuration class
- `LLMConfig`, `AudioConfig`, `VideoConfig`: Specialized config classes
- Environment variable support for all settings

#### 2. Domain Layer (`core/`)
- `VideoScript`: Represents the complete video script
- `ScriptSegment`: Individual script segments with timing
- `AudioData`: Audio file information with metadata
- Custom exceptions for different error types

#### 3. Service Layer (`services/`)
- `LLMService`: Handles content generation using LLMs
- `AudioService`: Manages text-to-speech and audio processing
- `VideoService`: Handles Manim video generation and audio combination
- `ScriptService`: Manages script creation, validation, and persistence

#### 4. Utilities (`utils/`)
- Logging setup and management
- Helper functions for text processing, file validation, and duration formatting

## Usage

### Basic Usage
```bash
# Run with default problem
python apps/main.py

# Run with custom problem
python apps/main.py "你的数学题目"

# Enable debug mode
python apps/main.py --debug
```

### Environment Variables
```bash
# LLM Configuration
export LLM_API_KEY="your_llm_api_key"
export LLM_MODEL="qwen-turbo"
export LLM_MAX_TOKENS="4000"

# Audio Configuration  
export OPENAI_API_KEY="your_openai_api_key"
export AUDIO_VOICE="alloy"
export AUDIO_SPEED="1.0"

# Video Configuration
export VIDEO_RESOLUTION="480p15"
export OUTPUT_DIR="output"

# Debug mode
export DEBUG="true"
```

## Benefits of This Architecture

1. **Maintainability**: Clear separation makes the code easier to understand and modify
2. **Testability**: Each service can be tested independently
3. **Scalability**: New features can be added without affecting existing code
4. **Reusability**: Services can be reused in different contexts
5. **Configuration**: Centralized configuration management
6. **Error Handling**: Structured error handling with meaningful messages

## Integration with Original Code

This new architecture is completely self-contained and doesn't reference the original codebase. You can:

1. Run the original system using `python main.py` (from root directory)
2. Run the new system using `python apps/main.py` (from apps directory)
3. Compare the two implementations
4. Gradually migrate features between them

The new architecture provides a solid foundation for future development while maintaining all the core functionality of the original system.