"""Core domain models for the math video application."""

from dataclasses import dataclass
from typing import List, Optional, Dict, Any


@dataclass
class AudioData:
    """Audio data with timing information."""
    file_path: str
    duration: float
    text: str
    segment_id: str


@dataclass
class ScriptSegment:
    """A segment of the teaching script."""
    id: str
    title: str
    content: str
    duration: Optional[float] = None
    audio_file: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            'id': self.id,
            'title': self.title,
            'content': self.content,
            'duration': self.duration,
            'audio_file': self.audio_file
        }


@dataclass
class VideoScript:
    """Complete video script with all segments."""
    problem: str
    segments: List[ScriptSegment]
    total_duration: Optional[float] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            'problem': self.problem,
            'segments': [segment.to_dict() for segment in self.segments],
            'total_duration': self.total_duration
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'VideoScript':
        """Create from dictionary."""
        segments = [
            ScriptSegment(
                id=seg['id'],
                title=seg['title'],
                content=seg['content'],
                duration=seg.get('duration'),
                audio_file=seg.get('audio_file')
            )
            for seg in data['segments']
        ]
        
        return cls(
            problem=data['problem'],
            segments=segments,
            total_duration=data.get('total_duration')
        )