"""LLM service for content generation."""

import json
import async<PERSON>
from typing import Dict, Any, List, Optional
from dataclasses import asdict

from ..config import LLMConfig
from ..core.exceptions import LLMError
from ..utils import get_logger
from ..prompts import GENERATE_VIDEO_SCRIPT_PROMPT, GENERATE_MATH_PROMPT


class LLMService:
    """Service for interacting with Large Language Models."""
    
    def __init__(self, config: LLMConfig):
        self.config = config
        self.logger = get_logger(__name__)
        
    async def generate_script(self, problem: str) -> Dict[str, Any]:
        """Generate teaching script for a math problem."""
        try:
            prompt = self._build_script_prompt(problem)
            response = await self._make_request(prompt)
            return self._parse_script_response(response)
        except Exception as e:
            raise LLMError(f"Failed to generate script: {str(e)}")
    
    async def generate_manim_code(
        self, 
        problem: str, 
        script_data: Dict[str, Any],
        audio_durations: Dict[str, float]
    ) -> str:
        """Generate Manim code for video animation."""
        try:
            prompt = self._build_manim_prompt(problem, script_data, audio_durations)
            response = await self._make_request(prompt)
            return self._extract_code_from_response(response)
        except Exception as e:
            raise LLMError(f"Failed to generate Manim code: {str(e)}")
    
    def _build_script_prompt(self, problem: str) -> str:
        """Build prompt for script generation."""
        return f"{GENERATE_VIDEO_SCRIPT_PROMPT}\n\n题目: {problem}"
    
    def _build_manim_prompt(
        self, 
        problem: str, 
        script_data: Dict[str, Any],
        audio_durations: Dict[str, float]
    ) -> str:
        """Build prompt for Manim code generation."""
        durations_text = "\n".join([
            f"- {seg_id}: {duration}秒"
            for seg_id, duration in audio_durations.items()
        ])
        
        return f"""{GENERATE_MATH_PROMPT}

## 题目信息
题目: {problem}

## 脚本数据
{json.dumps(script_data, ensure_ascii=False, indent=2)}

## 音频时长信息
{durations_text}

请根据以上信息生成完整的Manim代码。"""
    
    async def _make_request(self, prompt: str) -> str:
        """Make a request to the LLM API."""
        await asyncio.sleep(0.1)
        
        if "video_segment" in prompt or "脚本" in prompt:
            return '''{
    "problem_information": {
        "original_problem": "水果店有68个苹果，上午卖出了32个，下午又卖出了25个，还剩多少个苹果?",
        "problem_type": "两步减法应用题",
        "key_points": ["提取关键信息", "第一步计算", "第二步计算", "答案确认"]
    },
    "video_segment": [
        {
            "segment_number": 1,
            "segment_title": "提取关键信息",
            "duration": "8秒",
            "voice_over": "让我们先来看看这道题目。水果店有68个苹果，上午卖出了32个，下午又卖出了25个，我们要算出还剩多少个苹果。",
            "screen_display": {
                "main_content": "水果店有68个苹果\\n上午卖出32个\\n下午卖出25个\\n还剩多少个？",
                "display_method": "题目内容分行显示",
                "highlight": "数字用不同颜色标注",
                "animation": "内容逐行出现"
            }
        },
        {
            "segment_number": 2,
            "segment_title": "第一步计算",
            "duration": "6秒",
            "voice_over": "首先我们计算上午卖出32个后还剩多少。68减去32等于36个苹果。",
            "screen_display": {
                "main_content": "第一步：68 - 32 = 36",
                "display_method": "计算式居中显示",
                "highlight": "结果36用绿色高亮",
                "animation": "计算过程逐步显示"
            }
        },
        {
            "segment_number": 3,
            "segment_title": "第二步计算",
            "duration": "6秒",
            "voice_over": "然后我们从剩下的36个苹果中，再减去下午卖出的25个。36减去25等于11个苹果。",
            "screen_display": {
                "main_content": "第二步：36 - 25 = 11",
                "display_method": "计算式居中显示",
                "highlight": "结果11用绿色高亮",
                "animation": "计算过程逐步显示"
            }
        },
        {
            "segment_number": 4,
            "segment_title": "答案确认",
            "duration": "5秒",
            "voice_over": "所以最后还剩下11个苹果。让我们再检查一下：68减32等于36，36减25等于11。答案是11个苹果。",
            "screen_display": {
                "main_content": "答案：还剩11个苹果",
                "display_method": "答案大字体突出显示",
                "highlight": "答案用红色强调",
                "animation": "答案闪烁强调"
            }
        }
    ],
    "summary": {
        "title": "总结",
        "voice_over": "太棒了！我们通过两步减法，轻松算出了答案。你学会了吗？",
        "duration": "4秒",
        "screen_display": {
            "main_content": "68 - 32 - 25 = 11",
            "display_method": "完整算式展示",
            "highlight": "关键步骤用颜色区分",
            "animation": "算式完整显示"
        }
    }
}'''
        else:
            return '''
from manim import *

class AdaptiveMathLesson(Scene):
    def construct(self):
        # Title
        title = Text("苹果减法问题", font_size=48)
        self.play(Write(title))
        self.wait(2)
        self.play(FadeOut(title))
        
        # Problem statement
        problem = Text("水果店有68个苹果\\n上午卖出32个，下午卖出25个\\n还剩多少个？", 
                      font_size=36, line_spacing=1.5)
        self.play(Write(problem))
        self.wait(3)
        
        # Show calculation
        calc = MathTex("68 - 32 - 25 = ?")
        calc.next_to(problem, DOWN, buff=1)
        self.play(Write(calc))
        self.wait(2)
        
        # Show answer
        answer = MathTex("68 - 32 - 25 = 11")
        answer.move_to(calc.get_center())
        self.play(Transform(calc, answer))
        self.wait(3)
'''
    
    def _parse_script_response(self, response: str) -> Dict[str, Any]:
        """Parse script response from LLM."""
        try:
            if response.strip().startswith('{'):
                return json.loads(response)
            else:
                start = response.find('{')
                end = response.rfind('}') + 1
                if start >= 0 and end > start:
                    json_str = response[start:end]
                    return json.loads(json_str)
        except json.JSONDecodeError:
            pass
        
        raise LLMError("Could not parse script response as JSON")
    
    def _extract_code_from_response(self, response: str) -> str:
        """Extract Python code from LLM response."""
        lines = response.split('\n')
        code_lines = []
        in_code_block = False
        
        for line in lines:
            if line.strip().startswith('```python') or line.strip().startswith('```'):
                in_code_block = True
                continue
            elif line.strip() == '```' and in_code_block:
                break
            elif in_code_block:
                code_lines.append(line)
        
        if code_lines:
            return '\n'.join(code_lines)
        
        return response