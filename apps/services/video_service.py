"""Video generation service using Manim."""

import os
import subprocess
import tempfile
from pathlib import Path
from typing import Dict, Optional

from ..config import VideoConfig
from ..core.models import AudioData
from ..core.exceptions import VideoGenerationError
from ..utils import get_logger


class VideoService:
    """Service for video generation using Manim."""
    
    def __init__(self, config: VideoConfig):
        self.config = config
        self.logger = get_logger(__name__)
        
    async def generate_video(
        self, 
        manim_code: str, 
        audio_data: Dict[str, AudioData],
        scene_name: str = "AdaptiveMathLesson"
    ) -> str:
        """Generate video using Manim code and audio data."""
        try:
            video_path = await self._render_manim_video(manim_code, scene_name)
            
            if audio_data:
                combined_path = await self._combine_with_audio(video_path, audio_data)
                return combined_path
            
            return video_path
            
        except Exception as e:
            raise VideoGenerationError(f"Video generation failed: {str(e)}")
    
    async def _render_manim_video(self, manim_code: str, scene_name: str) -> str:
        """Render video using Manim."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
            f.write(manim_code)
            script_path = f.name
        
        try:
            output_dir = Path(self.config.output_dir)
            output_dir.mkdir(exist_ok=True)
            
            # Map resolution to Manim quality flags
            quality_map = {
                '480p15': 'l',  # low quality
                '720p30': 'm',  # medium quality  
                '1080p60': 'h', # high quality
                '1440p60': 'p', # production quality
                '2160p60': 'k'  # 4K quality
            }
            
            quality = quality_map.get(self.config.resolution, 'l')
            
            cmd = [
                'manim',
                'render',
                script_path,
                scene_name,
                '--quality', quality,
                '--output_file', 'math_lesson_video'
            ]
            
            self.logger.info(f"Running Manim command: {' '.join(cmd)}")
            
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                cwd=str(output_dir)
            )
            
            if result.returncode != 0:
                error_msg = f"Manim failed: {result.stderr}"
                self.logger.error(error_msg)
                raise VideoGenerationError(error_msg)
            
            video_files = list(output_dir.glob("**/*.mp4"))
            if not video_files:
                raise VideoGenerationError("No video file was generated")
            
            latest_video = max(video_files, key=lambda x: x.stat().st_mtime)
            self.logger.info(f"Generated video: {latest_video}")
            
            return str(latest_video)
            
        finally:
            if os.path.exists(script_path):
                os.unlink(script_path)
    
    async def _combine_with_audio(
        self, 
        video_path: str, 
        audio_data: Dict[str, AudioData]
    ) -> str:
        """Combine video with audio track."""
        try:
            from moviepy.editor import VideoFileClip, AudioFileClip, concatenate_audioclips
            
            video = VideoFileClip(video_path)
            
            audio_clips = []
            for data in audio_data.values():
                if os.path.exists(data.file_path):
                    audio_clip = AudioFileClip(data.file_path)
                    audio_clips.append(audio_clip)
            
            if audio_clips:
                combined_audio = concatenate_audioclips(audio_clips)
                
                if combined_audio.duration > video.duration:
                    combined_audio = combined_audio.subclip(0, video.duration)
                elif combined_audio.duration < video.duration:
                    video = video.subclip(0, combined_audio.duration)
                
                final_video = video.set_audio(combined_audio)
                
                output_path = video_path.replace('.mp4', '_with_audio.mp4')
                final_video.write_videofile(
                    output_path,
                    audio_codec='aac',
                    verbose=False,
                    logger=None
                )
                
                video.close()
                combined_audio.close()
                final_video.close()
                
                return output_path
            
            return video_path
            
        except ImportError:
            self.logger.warning("moviepy not available, returning video without audio")
            return video_path
        except Exception as e:
            self.logger.error(f"Failed to combine audio: {e}")
            return video_path