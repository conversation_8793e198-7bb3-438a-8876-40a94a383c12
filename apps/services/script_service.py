"""Script management service."""

import json
from pathlib import Path
from typing import Dict, Any, Optional

from ..core.models import VideoScript, ScriptSegment
from ..core.exceptions import ValidationError
from ..utils import get_logger, clean_text


class ScriptService:
    """Service for managing video scripts."""
    
    def __init__(self):
        self.logger = get_logger(__name__)
    
    def create_script_from_llm_response(
        self, 
        problem: str, 
        llm_response: Dict[str, Any]
    ) -> VideoScript:
        """Create VideoScript from LLM response."""
        try:
            self._validate_llm_response(llm_response)
            
            segments = []
            # Handle both old format (segments) and new format (video_segment)
            seg_list = llm_response.get('segments', llm_response.get('video_segment', []))
            
            for seg_data in seg_list:
                # Handle different field names
                seg_id = seg_data.get('id', seg_data.get('segment_title', f"segment_{seg_data.get('segment_number', len(segments)+1)}"))
                title = seg_data.get('title', seg_data.get('segment_title', 'Untitled'))
                content = seg_data.get('content', seg_data.get('voice_over', ''))
                
                segment = ScriptSegment(
                    id=seg_id,
                    title=title,
                    content=clean_text(content)
                )
                segments.append(segment)
            
            return VideoScript(
                problem=problem,
                segments=segments
            )
            
        except Exception as e:
            raise ValidationError(f"Failed to create script: {str(e)}")
    
    def save_script(self, script: VideoScript, file_path: str) -> None:
        """Save script to JSON file."""
        try:
            data = script.to_dict()
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            self.logger.info(f"Script saved to {file_path}")
        except Exception as e:
            raise ValidationError(f"Failed to save script: {str(e)}")
    
    def load_script(self, file_path: str) -> Optional[VideoScript]:
        """Load script from JSON file."""
        try:
            if not Path(file_path).exists():
                return None
                
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            return VideoScript.from_dict(data)
        except Exception as e:
            self.logger.error(f"Failed to load script from {file_path}: {e}")
            return None
    
    def update_segment_timing(
        self, 
        script: VideoScript, 
        audio_data: Dict[str, Any]
    ) -> VideoScript:
        """Update script segments with audio timing information."""
        total_duration = 0.0
        
        for segment in script.segments:
            if segment.id in audio_data:
                audio_info = audio_data[segment.id]
                segment.duration = audio_info.duration
                segment.audio_file = audio_info.file_path
                total_duration += audio_info.duration
        
        script.total_duration = total_duration
        return script
    
    def _validate_llm_response(self, response: Dict[str, Any]) -> None:
        """Validate LLM response structure."""
        # Handle both old format (segments) and new format (video_segment)
        if 'segments' not in response and 'video_segment' not in response:
            raise ValidationError("LLM response missing 'segments' or 'video_segment' field")
        
        segments = response.get('segments', response.get('video_segment', []))
        if not isinstance(segments, list) or len(segments) == 0:
            raise ValidationError("LLM response segments must be a non-empty list")
        
        for i, segment in enumerate(segments):
            if not isinstance(segment, dict):
                raise ValidationError(f"Segment {i} must be a dictionary")
            
            # Check for either old format fields or new format fields
            has_old_format = all(field in segment for field in ['id', 'title', 'content'])
            has_new_format = 'voice_over' in segment and ('segment_title' in segment or 'segment_number' in segment)
            
            if not has_old_format and not has_new_format:
                raise ValidationError(f"Segment {i} missing required fields. Need either (id, title, content) or (voice_over, segment_title/segment_number)")