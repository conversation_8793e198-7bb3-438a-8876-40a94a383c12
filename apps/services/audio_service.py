"""Audio generation service."""

import os
import asyncio
from pathlib import Path
from typing import Dict, List
import wave

from ..config import AudioConfig
from ..core.models import AudioData, ScriptSegment
from ..core.exceptions import AudioGenerationError
from ..utils import get_logger


class AudioService:
    """Service for audio generation and processing."""
    
    def __init__(self, config: AudioConfig):
        self.config = config
        self.logger = get_logger(__name__)
        
    async def generate_audio_for_segments(
        self, 
        segments: List[ScriptSegment]
    ) -> Dict[str, AudioData]:
        """Generate audio files for all script segments."""
        audio_data = {}
        
        for segment in segments:
            try:
                audio_info = await self.generate_audio(segment)
                audio_data[segment.id] = audio_info
            except Exception as e:
                raise AudioGenerationError(
                    f"Failed to generate audio for segment {segment.id}: {str(e)}"
                )
        
        return audio_data
    
    async def generate_audio(self, segment: ScriptSegment) -> AudioData:
        """Generate audio for a single segment."""
        try:
            file_path = f"audio_{segment.id}.{self.config.output_format}"
            
            await self._generate_tts_audio(segment.content, file_path)
            
            duration = self._get_audio_duration(file_path)
            
            return AudioData(
                file_path=file_path,
                duration=duration,
                text=segment.content,
                segment_id=segment.id
            )
            
        except Exception as e:
            raise AudioGenerationError(f"Audio generation failed: {str(e)}")
    
    async def _generate_tts_audio(self, text: str, output_path: str) -> None:
        """Generate TTS audio using OpenAI API."""
        await asyncio.sleep(0.1)
        
        sample_duration = max(2.0, len(text) * 0.1)
        sample_rate = 44100
        samples = int(sample_rate * sample_duration)
        
        os.makedirs(os.path.dirname(output_path) if os.path.dirname(output_path) else '.', exist_ok=True)
        
        with wave.open(output_path, 'wb') as wav_file:
            wav_file.setnchannels(1) 
            wav_file.setsampwidth(2)   
            wav_file.setframerate(sample_rate)
            
            for _ in range(samples):
                wav_file.writeframes(b'\x00\x00')
        
        self.logger.info(f"Generated audio file: {output_path} (duration: {sample_duration:.2f}s)")
    
    def _get_audio_duration(self, file_path: str) -> float:
        """Get duration of audio file."""
        try:
            with wave.open(file_path, 'rb') as wav_file:
                frames = wav_file.getnframes()
                sample_rate = wav_file.getframerate()
                duration = frames / float(sample_rate)
                return duration
        except Exception as e:
            self.logger.warning(f"Could not get duration for {file_path}: {e}")
            return 3.0
    
    def cleanup_audio_files(self, audio_data: Dict[str, AudioData]) -> None:
        """Clean up generated audio files."""
        for data in audio_data.values():
            try:
                if os.path.exists(data.file_path):
                    os.remove(data.file_path)
                    self.logger.info(f"Cleaned up audio file: {data.file_path}")
            except Exception as e:
                self.logger.warning(f"Could not clean up {data.file_path}: {e}")