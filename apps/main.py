"""
Math Video Generation Application - Clean Architecture Version

This is the main entry point for the math video generation system.
Uses a clean architecture with separated concerns and dependency injection.
"""

import asyncio
import sys
from pathlib import Path

from .config import AppSettings
from .core.models import VideoScript
from .core.exceptions import VideoGenerationError, AudioGenerationError, ValidationError
from .services import LLMService, AudioService, VideoService, ScriptService
from .utils import setup_logging, get_logger, format_duration


class MathVideoApp:
    """Main application class for math video generation."""
    
    def __init__(self, settings: AppSettings):
        self.settings = settings
        self.logger = get_logger(__name__)
        
        # Initialize services
        self.llm_service = LLMService(settings.llm)
        self.audio_service = AudioService(settings.audio)
        self.video_service = VideoService(settings.video)
        self.script_service = ScriptService()
    
    async def create_math_video(self, problem: str) -> bool:
        """Create a complete math video from a problem statement."""
        try:
            self.logger.info(f"Starting video creation for problem: {problem}")
            
            # Step 1: Generate teaching script
            self.logger.info("📝 Generating teaching script...")
            script_data = await self.llm_service.generate_script(problem)
            script = self.script_service.create_script_from_llm_response(problem, script_data)
            
            # Step 2: Generate audio for each segment
            self.logger.info("🎵 Generating audio for script segments...")
            audio_data = await self.audio_service.generate_audio_for_segments(script.segments)
            
            # Step 3: Update script with timing information
            script = self.script_service.update_segment_timing(script, audio_data)
            
            # Step 4: Save script for reference
            script_file = "generated_script.json"
            self.script_service.save_script(script, script_file)
            
            # Step 5: Generate Manim animation code
            self.logger.info("🎬 Generating animation code...")
            audio_durations = {seg_id: data.duration for seg_id, data in audio_data.items()}
            manim_code = await self.llm_service.generate_manim_code(
                problem, script_data, audio_durations
            )
            
            # Step 6: Render final video
            self.logger.info("🎥 Rendering final video...")
            video_path = await self.video_service.generate_video(manim_code, audio_data)
            
            # Step 7: Clean up temporary files
            if not self.settings.debug:
                self.audio_service.cleanup_audio_files(audio_data)
            
            self.logger.info(f"✅ Video created successfully: {video_path}")
            self.logger.info(f"📊 Total duration: {format_duration(script.total_duration or 0)}")
            
            return True
            
        except ValidationError as e:
            self.logger.error(f"❌ Validation error: {e}")
            return False
        except AudioGenerationError as e:
            self.logger.error(f"❌ Audio generation error: {e}")
            return False
        except VideoGenerationError as e:
            self.logger.error(f"❌ Video generation error: {e}")
            return False
        except Exception as e:
            self.logger.error(f"❌ Unexpected error: {e}")
            if self.settings.debug:
                raise
            return False


async def main():
    """Main application entry point."""
    
    # Setup logging
    setup_logging(
        level="DEBUG" if "--debug" in sys.argv else "INFO",
        format_string="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )
    
    logger = get_logger(__name__)
    
    # Load configuration
    try:
        settings = AppSettings.from_env()
        logger.info("🚀 Math Video Generation System - Clean Architecture")
        logger.info("=" * 60)
        
        if "--debug" in sys.argv:
            settings.debug = True
            logger.info("🐛 Debug mode enabled")
        
    except Exception as e:
        logger.error(f"❌ Failed to load configuration: {e}")
        sys.exit(1)
    
    # Get problem from command line or use default
    if len(sys.argv) > 1 and not sys.argv[1].startswith('--'):
        problem = sys.argv[1]
    else:
        problem = "水果店有68个苹果，上午卖出了32个，下午又卖出了25个，还剩多少个苹果?"
    
    logger.info(f"📚 Problem: {problem}")
    
    # Create and run application
    app = MathVideoApp(settings)
    success = await app.create_math_video(problem)
    
    if success:
        logger.info("🎉 Application completed successfully!")
        print("\n💡 Tips:")
        print("  • Audio timing automatically controls animation pace")
        print("  • Check generated_script.json for script details")
        print("  • Use --debug flag for verbose logging")
    else:
        logger.error("❌ Application failed. Check logs for details.")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())