"""Helper utility functions."""

import re
from pathlib import Path
from typing import Union


def format_duration(seconds: float) -> str:
    """Format duration in seconds to readable string."""
    if seconds < 60:
        return f"{seconds:.1f}s"
    elif seconds < 3600:
        minutes = int(seconds // 60)
        secs = seconds % 60
        return f"{minutes}m {secs:.1f}s"
    else:
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = seconds % 60
        return f"{hours}h {minutes}m {secs:.1f}s"


def clean_text(text: str) -> str:
    """Clean text for better processing."""
    text = re.sub(r'\s+', ' ', text.strip())
    text = re.sub(r'[^\w\s\u4e00-\u9fff，。！？；：、]', '', text)
    return text


def validate_file_path(path: Union[str, Path]) -> bool:
    """Validate if a file path exists and is readable."""
    try:
        path_obj = Path(path)
        return path_obj.exists() and path_obj.is_file()
    except (<PERSON>Error, ValueError):
        return False