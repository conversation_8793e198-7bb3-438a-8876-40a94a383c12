# =============================================================================
# Manim教学视频AI配音系统 - 音频优先版本
# 作者：Claude AI助手
# 用途：为小朋友制作带配音的数学教学视频（先生成语音，后匹配动画）
# =============================================================================

import json
import asyncio
from models.creator import AudioFirstLessonCreator


# =============================================================================
# 第五部分：主函数
# =============================================================================

async def main():
    """主函数 - 音频优先模式"""

    print("🎓 Manim教学视频AI配音系统 - 音频优先版本")
    print("为小朋友制作数学教学视频（先生成自然语音，再匹配动画）")
    print("=" * 60)
    query = "水果店有68个苹果，上午卖出了32个，下午又卖出了25个，还剩多少个苹果?"
    # 创建教学视频
    creator = AudioFirstLessonCreator()
    success = await creator.create_complete_lesson(query)

    if success:
        print("\n🎉 所有任务完成！")
        print("💡 提示：")
        print("  • 语音时长自动决定动画节奏")
        print("  • 可以修改script_templates来更改内容")
        print("  • 动画会自动适应语音的自然时长")
    else:
        print("\n❌ 制作失败，请检查错误信息")


if __name__ == "__main__":
    # 直接运行时启动主程序
    asyncio.run(main())
