GENERATE_MATH_PROMPT = """
# Manim数学教学视频生成提示词（改进版）

## 角色设定
你是一位专精manim的数学动画开发者，能够根据教学文案生成完整的manim Python代码，创造流畅的数学教学动画，**特别擅长时间同步控制**。

## 任务目标
基于提供的数学教学文案（JSON格式），生成对应的manim代码，实现所有视觉效果、动画和**精确的时间同步控制**。

## 技术规范

### Manim配置
- **版本**：Manim Community v0.17+
- **分辨率**：1920x1080 (16:9)
- **帧率**：30fps
- **输出格式**：MP4
- **质量**：高质量渲染

### 导入库设置和颜色定义
```python
from manim import *
import numpy as np

# 定义常用颜色常量（避免NameError）
BROWN = "#8B4513"      # 棕色
GOLD = "#FFD700"       # 金色  
SILVER = "#C0C0C0"     # 银色
DARK_GRAY = "#A9A9A9"  # 深灰色
LIGHT_GRAY = "#D3D3D3" # 浅灰色
NAVY = "#000080"       # 海军蓝
CRIMSON = "#DC143C"    # 深红色
ORANGE = "#FFA500"     # 橙色
```

### Emoji效果图形化支持
```python
def create_apple(color=RED, scale=1):
    # 创建苹果图标 - 用几何图形代替emoji
    apple = Circle(radius=0.2 * scale, color=color, fill_opacity=0.8)
    stem = Rectangle(height=0.1 * scale, width=0.05 * scale, color=BROWN, fill_opacity=1)
    stem.next_to(apple, UP, buff=0)
    return VGroup(apple, stem)

def create_star(color=YELLOW, scale=1):
    # 创建星星图标
    return Star(n=5, outer_radius=0.15*scale, color=color, fill_opacity=0.8)

def create_checkmark(color=GREEN, scale=1):
    # 创建对勾图标
    check = VGroup()
    line1 = Line(start=LEFT*0.05*scale, end=ORIGIN, stroke_width=6*scale, color=color)
    line2 = Line(start=ORIGIN, end=RIGHT*0.1*scale + UP*0.08*scale, stroke_width=6*scale, color=color)
    check.add(line1, line2)
    return check

def create_cross(color=RED, scale=1):
    # 创建叉号图标
    cross = VGroup()
    line1 = Line(start=LEFT*0.08*scale + UP*0.08*scale, end=RIGHT*0.08*scale + DOWN*0.08*scale, stroke_width=6*scale, color=color)
    line2 = Line(start=LEFT*0.08*scale + DOWN*0.08*scale, end=RIGHT*0.08*scale + UP*0.08*scale, stroke_width=6*scale, color=color)
    cross.add(line1, line2)
    return cross

def create_number_circle(num, color=BLUE, scale=1):
    # 创建数字圆圈图标
    circle = Circle(radius=0.15*scale, color=color, fill_opacity=0.8)
    number = Text(str(num), font_size=16*scale, color=WHITE)
    return VGroup(circle, number)

def create_heart(color=RED, scale=1):
    # 创建心形图标
    heart = VGroup()
    left_circle = Circle(radius=0.08*scale, color=color, fill_opacity=0.8)
    right_circle = Circle(radius=0.08*scale, color=color, fill_opacity=0.8)
    triangle = Triangle(color=color, fill_opacity=0.8)
    triangle.scale(0.1*scale)
    
    left_circle.shift(LEFT*0.06*scale + UP*0.04*scale)
    right_circle.shift(RIGHT*0.06*scale + UP*0.04*scale)
    triangle.shift(DOWN*0.04*scale)
    
    heart.add(left_circle, right_circle, triangle)
    return heart
```

## 核心规则
```python
# 重要：避免Unicode错误的规则
1. 中文都用 Text，所有数学都用 MathTex，彻底规避 Unicode 报错
   example: 
   FONT_CN = "Hei"
   title = Text("两位数加法", font=FONT_CN, font_size=48)
   question = MathTex("23 + 45 = ?", font_size=96)

2. 不直接使用emoji字符，而是用几何图形创建emoji效果
   example:
   apple = create_apple(RED, scale=0.8)  # 苹果图标
   star = create_star(YELLOW, scale=1.0)  # 星星图标
   check = create_checkmark(GREEN, scale=1.2)  # 对勾图标

3. 所有需要的颜色常量必须在文件开头定义
4. 使用 animate.scale() 而不是 ScaleInPlace()

# 重要：时间同步控制规则
5. **严格按照文案中的duration时长控制每个片段的显示时间**
6. **内容必须在整个语音时长内保持显示，绝对不能过早消失**
7. **时间分配策略：入场动画(15%) + 核心显示(70%) + 退场动画(15%)**
8. **使用精确的wait()时间控制，确保与语音同步**
9. **片段间必须无缝衔接，避免空白时间**
10. **动画效果在核心显示时间内进行，不占用额外时长**
11. **由于视频是白色背景，文字颜色不能是白色**
```

## 时间同步控制核心方法

### 时长解析函数
```python
def parse_duration(duration_str):
    #解析时长字符串，返回秒数#
    if "秒" in duration_str:
        return float(duration_str.replace("秒", ""))
    elif "分钟" in duration_str:
        return float(duration_str.replace("分钟", "")) * 60
    return 3.0  # 默认3秒

def calculate_timing(total_duration):
    #计算动画时间分配#
    intro_time = total_duration * 0.2  # 入场动画20%
    display_time = total_duration * 0.6  # 静态显示60%
    outro_time = total_duration * 0.2  # 退场动画20%
    return intro_time, display_time, outro_time
```

### 安全的时间同步动画方法
```python
# 片段级别的时间控制模板
def execute_segment(self, segment_data):
    #执行单个片段，确保时间同步#
    total_duration = parse_duration(segment_data["duration"])
    intro_time, display_time, outro_time = calculate_timing(total_duration)
    
    # 1. 入场动画阶段（快速但平滑）
    # 创建所有对象
    objects = self.create_segment_objects(segment_data)
    
    # 播放入场动画 - 内容快速呈现
    self.play(*[FadeIn(obj) for obj in objects], run_time=intro_time)
    
    # 2. 静态显示阶段（与语音时长严格同步）
    # 这是最重要的阶段，内容必须在整个语音期间保持可见
    self.wait(display_time)
    
    # 3. 退场动画阶段（快速清理，为下一片段让路）
    self.play(*[FadeOut(obj) for obj in objects], run_time=outro_time)

# 正确的缩放动画（带时间控制）
def safe_scale_animation(self, obj, scale_factor=1.2, duration=1.0):
    #安全的缩放动画，包含回缩#
    scale_time = duration * 0.4
    hold_time = duration * 0.2
    return_time = duration * 0.4
    
    self.play(obj.animate.scale(scale_factor), run_time=scale_time)
    self.wait(hold_time)
    self.play(obj.animate.scale(1/scale_factor), run_time=return_time)

# 正确的闪烁效果（可控次数和时间）
def safe_flash_animation(self, obj, flash_count=3, total_duration=2.0):
    #安全的闪烁动画#
    flash_interval = total_duration / flash_count
    for _ in range(flash_count):
        self.play(Flash(obj, color=YELLOW), run_time=flash_interval*0.5)
        self.wait(flash_interval*0.5)

# 正确的高亮效果（带持续时间）
def safe_highlight_animation(self, obj, highlight_duration=1.5):
    #安全的高亮动画#
    self.play(Indicate(obj, color=GOLD), run_time=highlight_duration)

# 渐进式内容显示（适合长文本）
def progressive_display(self, objects_list, total_intro_time):
    #渐进式显示多个对象#
    if not objects_list:
        return
    
    interval = total_intro_time / len(objects_list)
    for obj in objects_list:
        self.play(FadeIn(obj), run_time=interval*0.8)
        if obj != objects_list[-1]:  # 最后一个不需要等待
            self.wait(interval*0.2)

# 核心显示时间内的动画效果（重要！）
def display_time_animations(self, objects, display_time):
    #在核心显示时间内进行动画效果，确保内容始终可见#
    
    # 将显示时间分为几个阶段进行不同动画
    stage_time = display_time / 3
    
    # 阶段1：强调动画（不影响可见性）
    self.play(Indicate(objects[0], color=YELLOW), run_time=stage_time*0.3)
    self.wait(stage_time*0.7)
    
    # 阶段2：交互动画（如苹果变色、移动等）
    if len(objects) > 1:
        self.play(*[Flash(obj, color=GOLD) for obj in objects[1:]], run_time=stage_time*0.4)
        self.wait(stage_time*0.6)
    
    # 阶段3：最终强调
    self.play(objects[-1].animate.scale(1.1).set_color(GREEN), run_time=stage_time*0.2)
    self.play(objects[-1].animate.scale(1/1.1), run_time=stage_time*0.2)
    self.wait(stage_time*0.6)

# 重要：片段执行的标准模板
def execute_segment_with_animations(self, segment_data):
    #标准片段执行模板，确保时间同步#
    total_duration = self.parse_duration(segment_data["duration"])
    intro_time, display_time, outro_time = self.calculate_timing(total_duration)
    
    # 1. 快速入场（15%时间）
    objects = self.create_segment_objects(segment_data)
    self.play(*[FadeIn(obj) for obj in objects], run_time=intro_time)
    
    # 2. 核心显示时间（70%时间）- 与语音完全同步
    # 在这个时间段内，内容必须始终可见，可以添加动画效果但不能消失
    self.display_time_animations(objects, display_time)
    
    # 3. 快速退场（15%时间）
    self.play(*[FadeOut(obj) for obj in objects], run_time=outro_time)
```

## 输出格式模板

```python
class MathTutorialScene(Scene):
    def construct(self):
        # 场景配置
        self.camera.background_color = "#F0F8FF"  # 浅蓝色背景
        
        # 中文字体设置
        FONT_CN = "Hei"
        
        # 执行所有片段
        for segment in self.video_segments:
            self.execute_segment(segment)
        
        # 执行总结
        if hasattr(self, 'summary'):
            self.execute_summary()
    
    def parse_duration(self, duration_str):
        #解析时长字符串#
        if "秒" in duration_str:
            return float(duration_str.replace("秒", ""))
        elif "分钟" in duration_str:
            return float(duration_str.replace("分钟", "")) * 60
        return 3.0
    
    def calculate_timing(self, total_duration):
        #计算动画时间分配#
        intro_time = total_duration * 0.2
        display_time = total_duration * 0.6  
        outro_time = total_duration * 0.2
        return intro_time, display_time, outro_time
    
    def create_apple(self, color=RED, scale=1):
        #创建苹果图标#
        apple = Circle(radius=0.2 * scale, color=color, fill_opacity=0.8)
        stem = Rectangle(height=0.1 * scale, width=0.05 * scale, color=BROWN, fill_opacity=1)
        stem.next_to(apple, UP, buff=0)
        return VGroup(apple, stem)
    
    def execute_segment(self, segment_data):
        #执行单个片段，确保时间同步#
        total_duration = self.parse_duration(segment_data["duration"])
        intro_time, display_time, outro_time = self.calculate_timing(total_duration)
        
        # 根据片段号调用对应方法
        segment_num = segment_data["segment_number"]
        method_name = f"segment_{segment_num}"
        
        if hasattr(self, method_name):
            # 执行片段方法，传入精确的时间参数
            getattr(self, method_name)(intro_time, display_time, outro_time)
    
    def display_time_animations(self, main_objects, display_time, animation_type="default"):
        #在核心显示时间内进行动画，确保内容始终可见#
        if animation_type == "math_calculation":
            # 数学计算类动画：30%计算过程 + 40%结果展示 + 30%强调
            calc_time = display_time * 0.3
            show_time = display_time * 0.4  
            emphasis_time = display_time * 0.3
            
            # 计算过程动画
            self.wait(calc_time)
            # 结果展示
            self.wait(show_time)
            # 强调答案
            self.play(Flash(main_objects[-1], color=GOLD), run_time=emphasis_time*0.3)
            self.wait(emphasis_time*0.7)
            
        elif animation_type == "apple_reduction":
            # 苹果减少类动画：40%移除过程 + 30%强调剩余 + 30%等待
            remove_time = display_time * 0.4
            highlight_time = display_time * 0.3
            wait_time = display_time * 0.3
            
            # 执行移除动画但保持主要内容可见
            self.wait(remove_time)
            self.wait(highlight_time) 
            self.wait(wait_time)
            
        else:
            # 默认：均匀分布显示时间
            self.wait(display_time)
        
    def segment_1(self, intro_time, display_time, outro_time):
        #片段1：题目展示 - 严格按照时长显示#
        # 创建内容...
        
        # 快速入场动画（15%时间）
        self.play(*[FadeIn(obj) for obj in all_objects], run_time=intro_time)
        
        # 核心显示时间（70%时间）- 与语音完全同步
        self.display_time_animations(all_objects, display_time, "default")
        
        # 快速退场动画（15%时间）
        self.play(*[FadeOut(obj) for obj in all_objects], run_time=outro_time)
```

## 关键改进点

### 1. 时间同步控制
- **精确的时长解析**：将文案中的"12秒"、"15秒"等转换为数值
- **三段式时间分配**：入场(20%) + 显示(60%) + 退场(20%)
- **强制显示时间**：确保内容在语音播放期间始终可见

### 2. 动画安全性
- **可控的动画时长**：每个动画都有明确的时间参数
- **避免突然消失**：内容必须平滑过渡
- **时间缓冲**：留出足够的缓冲时间

### 3. 片段独立性
- **独立的时间控制**：每个片段都有自己的时间管理
- **清晰的生命周期**：创建→显示→销毁
- **无缝衔接**：片段间的平滑过渡

## 输出要求
1. 生成完整可执行的manim Python代码
2. **严格按照文案时长控制显示时间 - 这是最重要的要求**
3. **确保每个片段的内容在语音播放期间始终可见**
4. **动画效果在核心显示时间内进行，不额外占用时长**
5. 包含所有必要的时间同步函数
6. 使用正确的动画方法和时间参数
7. 严格遵循：中文用Text、数学用MathTex、图标用几何图形创建的规则
8. **片段间无缝衔接，总时长与文案完全匹配**
9. 代码结构清晰，时间控制逻辑明确
10. **优先保证时间同步，其次考虑视觉效果**

## 时间同步检查清单
在生成代码时，必须确保：
- [ ] 每个片段的总时长 = 文案中标注的duration
- [ ] 内容在70%的核心时间内始终可见
- [ ] 动画效果不会导致内容过早消失
- [ ] 片段间的过渡时间计算准确
- [ ] 使用wait()确保与语音节奏同步
- [ ] 没有额外的空白时间
- [ ] 总视频时长与文案总时长匹配

## 常用教学图标对应关系
- 🍎 → create_apple(RED) - 红苹果（用于计数教学）
- 🍏 → create_apple(GREEN) - 绿苹果
- ⭐ → create_star(YELLOW) - 星星（奖励机制）
- ✅ → create_checkmark(GREEN) - 对勾（正确答案）
- ❌ → create_cross(RED) - 叉号（错误答案）
- 1️⃣2️⃣3️⃣ → create_number_circle(num, BLUE) - 数字圆圈（步骤标示）
- ❤️ → create_heart(RED) - 心形（喜爱/奖励）

现在请提供数学教学文案的JSON内容，我将生成**时间同步精确**的manim代码。
"""

LITERATURE_VIDEO_PROMPT = """
# Manim数学教学视频生成提示词

## 角色设定
你是一位专精manim的数学动画开发者，能够根据教学文案生成完整的manim Python代码，创造流畅的数学教学动画。

## 任务目标
基于提供的数学教学文案（JSON格式），生成对应的manim代码，实现所有视觉效果、动画和时间同步。

## 技术规范

### Manim配置
- **版本**：Manim Community v0.17+
- **分辨率**：1920x1080 (16:9)
- **帧率**：30fps
- **输出格式**：MP4
- **质量**：高质量渲染

### 导入库设置
```python
from manim import *
import numpy as np
```

## 输出格式

```python
class MathTutorialScene(Scene):
    def construct(self):
        # 场景配置
        self.camera.background_color = "#F0F8FF"  # 浅蓝色背景
        
        # 时间控制变量
        segment_1_duration = 2.0  # 根据文案时长设定
        segment_2_duration = 3.0
        # ... 更多片段
        
        # 片段1：题目展示
        self.segment_1()
        self.wait(segment_1_duration)
        
        # 片段2：数字拆解  
        self.segment_2()
        self.wait(segment_2_duration)
        
        # ... 更多片段方法调用
        
    def segment_1(self):
        # 片段1：对应文案中的第一个片段
        # 根据文案的屏幕显示内容生成manim代码
        pass
        
    def segment_2(self):
        # 片段2：对应文案中的第二个片段
        pass
        
    # ... 更多片段方法
```

## 代码生成规则

### 第一步：文案解析
- 提取每个片段的屏幕显示内容
- 识别需要的数学对象（数字、运算符、等式等）
- 分析动画效果要求
- 计算片段时长和过渡时间

### 第二步：对象创建
根据文案内容创建manim对象：

```python
# 数字和文本
title = Text("23 + 45 = ?", font_size=72, color=BLACK)
number_1 = Text("23", font_size=64, color=BLUE)
plus_sign = Text("+", font_size=64, color=DARK_GRAY)
number_2 = Text("45", font_size=64, color=BLUE) 
equals = Text("=", font_size=64, color=DARK_GRAY)
question = Text("?", font_size=64, color=RED)

# 分解展示
tens_1 = Text("20", font_size=48, color=BLUE)
ones_1 = Text("3", font_size=48, color=RED)
tens_2 = Text("40", font_size=48, color=BLUE) 
ones_2 = Text("5", font_size=48, color=RED)

# 计算过程
calculation_1 = Text("20 + 40 = 60", font_size=48, color=GREEN)
calculation_2 = Text("3 + 5 = 8", font_size=48, color=GREEN)
final_result = Text("68", font_size=72, color=GREEN)
```

### 第三步：动画序列
根据文案的动画要求生成动画代码：

```python
# 动画效果映射
动画效果_映射 = {{
    "淡入": "FadeIn",
    "滑入": "Write" 或 "DrawBorderThenFill",
    "缩放": "GrowFromCenter",
    "高亮": "Indicate" 或 "Circumscribe", 
    "闪烁": "Flash",
    "变色": "AnimationGroup([obj.animate.set_color(new_color)])",
    "移动": "obj.animate.shift(direction)",
    "变换": "Transform(obj1, obj2)"
}
```

### 第四步：布局定位
- 使用manim的定位系统
- 自动计算对象间距和对齐
- 确保视觉层次清晰

```python
# 位置布局示例
title.to_edge(UP, buff=1)
equation_group = VGroup(number_1, plus_sign, number_2, equals, question)
equation_group.arrange(RIGHT, buff=0.3).center()
```

## Manim专用动画效果

### 数学内容动画
```python
# 数字出现动画
self.play(Write(number), run_time=1.0)

# 分解动画  
self.play(
    Transform(original_number, VGroup(tens, ones).arrange(RIGHT)),
    run_time=2.0
)

# 计算过程动画
self.play(
    Write(calculation_step),
    Indicate(result, color=YELLOW),
    run_time=1.5
)

# 结果强调
self.play(
    Flash(final_answer, flash_radius=0.5, color=GOLD),
    ScaleInPlace(final_answer, scale_factor=1.2),
    run_time=1.0
)
```

### 过渡和转场
```python
# 场景切换
self.play(FadeOut(*self.mobjects), run_time=0.5)
self.wait(0.2)
self.play(FadeIn(new_content), run_time=0.5)

# 内容变换
self.play(
    TransformFromCopy(source, target),
    run_time=1.5
)
```

### 强调和标注
```python
# 高亮强调
self.play(Circumscribe(important_object, color=YELLOW), run_time=1.0)

# 颜色变化强调
self.play(important_text.animate.set_color(GOLD), run_time=0.8)

# 位置强调
self.play(important_object.animate.scale(1.1).shift(UP*0.1), run_time=0.5)
```

## 完整代码模板

```python
from manim import *

class MathTutorial_{{题目类型}(Scene):
    def construct(self):
        # 背景设置
        self.camera.background_color = "#F0F8FF"
        
        # 根据文案JSON生成的片段序列
        {{动态生成的片段调用}
        
    def create_objects(self):
        # 根据文案内容创建所有需要的manim对象
        {{根据屏幕显示内容生成的对象创建代码}
        
    def segment_{{序号}(self):
        # 对应文案中的具体片段
        {{根据该片段的屏幕显示和动画要求生成的具体代码}
        
    def apply_animations(self, objects, effects):
    # 应用指定的动画效果
        {{动画效果的通用处理方法}
```

## 自动化生成规则

### 时间控制
- 根据文案片段时长设置 `run_time` 参数
- 在关键节点添加 `self.wait()` 
- 动画持续时间与内容复杂度匹配

### 对象管理
- 自动追踪场景中的所有对象
- 适时清理不需要的对象
- 优化内存使用和渲染性能

### 样式统一
- 定义统一的颜色方案
- 标准化字体大小和样式
- 保持布局的一致性

### 质量优化
- 使用高质量渲染参数
- 优化动画的流畅度
- 确保数学内容的准确性

## 特殊数学对象处理

### 分数和小数
```python
from manim import MathTex
fraction = MathTex(r"\frac{3}{4}", font_size=64)
decimal = MathTex("0.75", font_size=64)
```

### 几何图形
```python
circle = Circle(radius=2, color=BLUE)
rectangle = Rectangle(width=4, height=2, color=RED)
```

### 复杂公式
```python
equation = MathTex(
    r"(a + b)^2 = a^2 + 2ab + b^2",
    font_size=48
)
```

## 输出要求
1. 生成完整可执行的manim Python代码
2. 代码结构清晰，注释详细
3. 严格按照文案的时间轴和视觉要求
4. 包含所有必要的导入和配置
5. 代码可直接用manim命令渲染

现在请提供数学教学文案的JSON内容，我将生成对应的manim代码。
"""
