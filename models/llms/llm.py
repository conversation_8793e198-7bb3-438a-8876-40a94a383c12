import asyncio
import traceback
from typing import List, Dict, Optional

from openai import AsyncStream, AsyncOpenAI, NOT_GIVEN
from openai.types.chat import ChatCompletion, ChatCompletionChunk

from configs.constant import ROLE, SYSTEM, CONTENT, LANGUAGE, IS_REASONING, QWEN_BASE_URL, BASE_URL_NAME, API_KEY_NAME, \
    NAME, QWEN_PLUS_MODEL, QWEN_API_KEY, QWEN_PLUS_LLM_MODEL_DATA, CLAUDE_MAX_LENGTH
from models.logger import logger
from models.prompts.system_prompt import DEFAULT_SYSTEM_MESSAGE, THINK_SYSTEM_PROMPT
from models.utils import convert_text_to_message
from openai import APIError, APIConnectionError, RateLimitError, APITimeoutError


def _client(base_url, api_key):
    return AsyncOpenAI(api_key=api_key, base_url=base_url)


async def llm_chat_v2(
        messages: List[Dict],
        llm_data: Dict = None,
        stream: bool = True,
        tools: Optional[List[Dict]] = None,
        extra_generate_cfg: Optional[Dict] = None,
        system_message: str = None,
        max_retries: int = 3,
        retry_delay: float = 1.0,
        backoff_factor: float = 2.0,
) -> ChatCompletion | AsyncStream[ChatCompletionChunk] | None:
    """
    Handles chat completion requests with optional streaming and retry functionality.
    If initial retries fail, fallback to QWEN model configuration.

    Args:
        messages: List of message dictionaries
        llm_data: LLM configuration data
        stream: Whether to stream the response
        tools: Optional list of tools to provide to the LLM
        extra_generate_cfg: Additional generation configuration parameters
        system_message: Optional system message to prepend
        max_retries: Maximum number of retry attempts (default: 3)
        retry_delay: Initial delay between retries in seconds (default: 1.0)
        backoff_factor: Multiplier for delay between retries (default: 2.0)

    Returns:
        ChatCompletion or AsyncStream[ChatCompletionChunk] depending on stream parameter
    """

    llm_data = llm_data if llm_data else QWEN_PLUS_LLM_MODEL_DATA
    base_url: str = llm_data.get(BASE_URL_NAME, QWEN_BASE_URL)
    api_key: str = llm_data.get(API_KEY_NAME, QWEN_API_KEY)
    model: str = llm_data.get(NAME, QWEN_PLUS_MODEL)

    if not system_message:
        if llm_data.get(IS_REASONING, False):
            system_message = system_message if system_message else DEFAULT_SYSTEM_MESSAGE
        else:
            system_message = THINK_SYSTEM_PROMPT

    if not tools:
        tools = None
    if messages[0][ROLE] != SYSTEM:
        messages = [convert_text_to_message(text=system_message, role=SYSTEM)] + messages
    else:
        messages.insert(0, {ROLE: SYSTEM, CONTENT: system_message})

    async def try_with_config(_base_url, _api_key, _model, is_fallback=False, _extra_generate_cfg: Dict = None):
        """Helper function to try API call with given configuration"""
        if not _extra_generate_cfg:
            _extra_generate_cfg = {}

        if LANGUAGE in _extra_generate_cfg:
            _extra_generate_cfg.pop(LANGUAGE)
        attempt = 0
        current_delay = retry_delay
        client = _client(_base_url, _api_key)
        if "claude" in _model.lower() or 'o1' in _model.lower():
            _extra_generate_cfg['max_tokens'] = CLAUDE_MAX_LENGTH
        while attempt < max_retries + 1:  # +1 to include the initial attempt
            try:
                if not tools:
                    tools_param = NOT_GIVEN
                else:
                    tools_param = tools

                completion = await client.chat.completions.create(
                    model=_model,
                    messages=messages,
                    tools=tools_param,
                    stream=stream,
                    **_extra_generate_cfg
                )
                return completion

            except (APIError, APIConnectionError, RateLimitError, APITimeoutError) as e:
                attempt += 1

                if attempt > max_retries:
                    config_type = "fallback QWEN" if is_fallback else "primary"
                    logger.error(
                        f"Failed after {max_retries} retries with {config_type} config. Last error: {str(e)}")
                    if is_fallback:
                        raise  # Re-raise if even fallback fails
                    return None  # Return None to indicate primary config failure

                # Log retry information
                config_type = "fallback QWEN" if is_fallback else "primary"
                retry_msg = f"API call failed with {config_type} config (attempt {attempt}/{max_retries}): {str(e)}. Retrying in {current_delay:.2f}s..."
                logger.warning(retry_msg)

                # Wait with exponential backoff
                await asyncio.sleep(current_delay)
                current_delay *= backoff_factor

            except Exception as e:
                # For other exceptions, we don't retry as they might be programming errors
                logger.error(
                    f"Unexpected error in llm_chat_v2 with {'fallback QWEN' if is_fallback else 'primary'} config: {str(e)}")
                logger.error(traceback.format_exc())
                raise

    # First try with original configuration
    try:
        result = await try_with_config(
            base_url, api_key, model, is_fallback=False, _extra_generate_cfg=extra_generate_cfg
        )
        if result is not None:
            return result
    except Exception as e:
        # If there's an unexpected error in the primary config, still try fallback
        logger.error(f"Unexpected error with primary config, trying fallback: {str(e)}")

    # If primary configuration failed after retries, try with QWEN fallback
    logger.warning(
        f"Primary configuration failed after {max_retries} retries. Switching to QWEN fallback configuration.")
