# from manim import *
# import json
# from typing import Dict
#
#
# class AdaptiveMathLesson(Scene):
#     """自适应时长的数学加法教学动画"""
#
#     def __init__(self, audio_durations: Dict[str, float], **kwargs):
#         super().__init__(**kwargs)
#         self.audio_durations = audio_durations
#         self.timeline = []
#         self.current_time = 0
#
#     def record_scene(self, scene_name: str, actual_duration: float, description: str = ""):
#         """记录场景时间轴（使用实际音频时长）"""
#         self.timeline.append({
#             "scene": scene_name,
#             "start": self.current_time,
#             "end": self.current_time + actual_duration,
#             "duration": actual_duration,
#             "description": description
#         })
#         self.current_time += actual_duration
#
#     def construct(self):
#         """构建自适应时长的数学教学动画"""
#
#         # 场景1：开场问候
#         opening_duration = self.audio_durations.get("opening", 3)
#         self.record_scene("opening", opening_duration, "欢迎和介绍今天的学习内容")
#
#         title = Text("今天我们学习加法", font_size=48, color=YELLOW)
#         # 动画时长自适应音频时长
#         write_time = min(opening_duration * 0.6, 2)  # 写字时间不超过音频的60%
#         wait_time = opening_duration - write_time
#
#         self.play(Write(title), run_time=write_time)
#         if wait_time > 0:
#             self.wait(wait_time)
#
#         # 场景2：清除标题，准备开始
#         prep_duration = self.audio_durations.get("preparation", 2)
#         self.record_scene("preparation", prep_duration, "准备开始学习")
#
#         fade_time = min(prep_duration * 0.5, 1)
#         wait_time = prep_duration - fade_time
#
#         self.play(FadeOut(title), run_time=fade_time)
#         if wait_time > 0:
#             self.wait(wait_time)
#
#         # 场景3：展示第一个数字
#         first_num_duration = self.audio_durations.get("first_number", 3)
#         self.record_scene("first_number", first_num_duration, "介绍第一个数字2")
#
#         num1 = Text("2", font_size=96, color=BLUE)
#         num1.move_to(LEFT * 3)
#
#         write_time = min(first_num_duration * 0.4, 1.5)
#         wait_time = first_num_duration - write_time
#
#         self.play(Write(num1), run_time=write_time)
#         if wait_time > 0:
#             self.wait(wait_time)
#
#         # 场景4：展示加号
#         plus_duration = self.audio_durations.get("plus_sign", 2)
#         self.record_scene("plus_sign", plus_duration, "介绍加号的含义")
#
#         plus = Text("+", font_size=96, color=WHITE)
#         plus.move_to(LEFT * 1)
#
#         write_time = min(plus_duration * 0.4, 1)
#         wait_time = plus_duration - write_time
#
#         self.play(Write(plus), run_time=write_time)
#         if wait_time > 0:
#             self.wait(wait_time)
#
#         # 场景5：展示第二个数字
#         second_num_duration = self.audio_durations.get("second_number", 3)
#         self.record_scene("second_number", second_num_duration, "介绍第二个数字3")
#
#         num2 = Text("3", font_size=96, color=RED)
#         num2.move_to(RIGHT * 1)
#
#         write_time = min(second_num_duration * 0.4, 1.5)
#         wait_time = second_num_duration - write_time
#
#         self.play(Write(num2), run_time=write_time)
#         if wait_time > 0:
#             self.wait(wait_time)
#
#         # 场景6：思考时间
#         thinking_duration = self.audio_durations.get("thinking", 2)
#         self.record_scene("thinking", thinking_duration, "让孩子思考答案")
#
#         thinking = Text("= ?", font_size=96, color=ORANGE)
#         thinking.move_to(RIGHT * 3)
#
#         write_time = min(thinking_duration * 0.3, 1)
#         wait_time = thinking_duration - write_time
#
#         self.play(Write(thinking), run_time=write_time)
#         if wait_time > 0:
#             self.wait(wait_time)
#
#         # 场景7：展示答案
#         answer_duration = self.audio_durations.get("answer", 3)
#         self.record_scene("answer", answer_duration, "揭示答案并庆祝")
#
#         answer = Text("5", font_size=96, color=GREEN)
#         answer.move_to(RIGHT * 3)
#
#         # 分配时间：变换 + 庆祝文字 + 等待
#         transform_time = min(answer_duration * 0.3, 1)
#         celebration_time = min(answer_duration * 0.3, 1)
#         wait_time = answer_duration - transform_time - celebration_time
#
#         self.play(Transform(thinking, answer), run_time=transform_time)
#
#         # 添加庆祝效果
#         celebration = Text("太棒了！", font_size=36, color=YELLOW)
#         celebration.move_to(UP * 2)
#         self.play(Write(celebration), run_time=celebration_time)
#
#         if wait_time > 0:
#             self.wait(wait_time)
#
#         # 场景8：总结
#         summary_duration = self.audio_durations.get("summary", 3)
#         self.record_scene("summary", summary_duration, "总结学习内容")
#
#         summary = Text("2 + 3 = 5", font_size=48, color=WHITE)
#         summary.move_to(DOWN * 2)
#
#         write_time = min(summary_duration * 0.5, 1.5)
#         wait_time = summary_duration - write_time
#
#         self.play(Write(summary), run_time=write_time)
#         if wait_time > 0:
#             self.wait(wait_time)
#
#         # 保存最终时间轴
#         self.save_timeline()
#
#     def save_timeline(self):
#         """保存时间轴到JSON文件"""
#         timeline_file = "timeline.json"
#         with open(timeline_file, "w", encoding="utf-8") as f:
#             json.dump(self.timeline, f, ensure_ascii=False, indent=2)
#         print(f"✅ 自适应时间轴已保存到: {timeline_file}")
