from typing import List, Dict, Optional
from pydantic import BaseModel, Field
import json


class ScreenDisplay(BaseModel):
    main_content: str = Field(..., description="主要内容")
    display_method: str = Field(..., description="显示方式")
    highlight: str = Field(..., description="重点标注")
    animation: str = Field(..., description="动画效果")

    def to_dict(self) -> Dict:
        return self.model_dump()

    @classmethod
    def from_dict(cls, data: Dict) -> 'ScreenDisplay':
        return cls(**data)


class VideoSegment(BaseModel):
    segment_number: Optional[int] = Field(None, description="片段序号")
    video_segment: Optional[int] = Field(None, description="Alternative segment number field")
    segment_title: str = Field(..., description="片段标题")
    duration: str = Field(..., description="时长")
    voice_over: str = Field(..., description="语音文案")
    audio_file: Optional[str] = Field(None, description="时长")
    screen_display: ScreenDisplay = Field(..., description="屏幕显示")

    def __init__(self, **data):
        if 'video_segment' in data and 'segment_number' not in data:
            data['segment_number'] = data['video_segment']
        super().__init__(**data)

    def to_dict(self) -> Dict:
        data = self.model_dump()
        if 'video_segment' in data:
            del data['video_segment']
        return data

    @classmethod
    def from_dict(cls, data: Dict) -> 'VideoSegment':
        return cls(**data)


class Summary(BaseModel):
    title: str = Field(..., description="总结标题")
    voice_over: str = Field(..., description="语音文案")
    duration: str = Field(..., description="时长")
    audio_file: Optional[str] = Field(None, description="时长")
    screen_display: ScreenDisplay = Field(..., description="屏幕显示")

    def to_dict(self) -> Dict:
        return self.model_dump()

    @classmethod
    def from_dict(cls, data: Dict) -> 'Summary':
        return cls(**data)


class ProblemInformation(BaseModel):
    original_problem: str = Field(..., description="原题")
    problem_type: str = Field(..., description="题目类型")
    key_points: List[str] = Field(..., description="主要拆解点")

    def to_dict(self) -> Dict:
        return self.model_dump()

    @classmethod
    def from_dict(cls, data: Dict) -> 'ProblemInformation':
        return cls(**data)


class VideoScript(BaseModel):
    problem_information: ProblemInformation = Field(..., alias="problem_information")
    video_segment: List[VideoSegment] = Field(..., alias="video_segment")
    summary: Summary = Field(..., alias="summary")

    def to_dict(self) -> Dict:
        return {
            "problem_information": self.problem_information.to_dict(),
            "video_segment": [segment.to_dict() for segment in self.video_segment],
            "summary": self.summary.to_dict()
        }

    def to_json(self, indent: Optional[int] = None) -> str:
        return json.dumps(self.to_dict(), ensure_ascii=False, indent=indent)

    @classmethod
    def from_dict(cls, data: Dict) -> 'VideoScript':
        return cls(
            problem_information=ProblemInformation.from_dict(data["problem_information"]),
            video_segment=[VideoSegment.from_dict(segment) for segment in data["video_segment"]],
            summary=Summary.from_dict(data["summary"])
        )

    @classmethod
    def from_json(cls, json_str: str) -> 'VideoScript':
        data = json.loads(json_str)
        return cls.from_dict(data)


# Example usage
if __name__ == "__main__":
    input_data = {
        "problem_information": {
            "original_problem": "例：23 + 45 = ?",
            "problem_type": "例：两位数加法",
            "key_points": ["十位数相加", "个位数相加", "进位处理"]
        },
        "video_segment": [
            {
                "segment_number": 1,
                "segment_title": "题目展示",
                "duration": "15秒",
                "voice_over": "小朋友们好！今天我们来解决这道加法题：23加45等于多少呢？",
                "screen_display": {
                    "main_content": "23 + 45 = ?",
                    "display_method": "完整题目居中显示，字体大而清晰",
                    "highlight": "暂无",
                    "animation": "题目从左侧滑入"
                }
            },
            {
                "video_segment": 2,
                "segment_title": "数字拆解",
                "duration": "20秒",
                "voice_over": "首先，我们把这两个数字分别拆开来看。23可以分成20和3，45可以分成40和5。",
                "screen_display": {
                    "main_content": "23 = 20 + 3\n45 = 40 + 5",
                    "display_method": "竖直排列，每行分解一个数字",
                    "highlight": "十位数用蓝色，个位数用红色",
                    "animation": "数字逐步分解，颜色区分显示"
                }
            }
        ],
        "summary": {
            "voice_over": "太棒了！我们通过拆分数字的方法，轻松算出了答案。你学会了吗？",
            "screen_display": {
                "main_content": "拆分法：分别计算十位和个位，再合并结果",
                "display_method": "总结要点，简洁明了",
                "highlight": "关键词用彩色标注",
                "animation": "要点逐条显示"
            }
        }
    }

    # Create instance from dict
    script = VideoScript.from_dict(input_data)
    print("Created from dict successfully!")

    # Convert to dict
    script_dict = script.to_dict()
    print("\nDictionary representation:")
    print(script_dict)

    # Convert to JSON
    script_json = script.to_json(indent=2)
    print("\nJSON representation:")
    print(script_json)

    # Create from JSON
    script_from_json = VideoScript.from_json(script_json)
    print("\nRecreated from JSON successfully!")
