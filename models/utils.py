from typing import Dict
import re
from typing import List, Tuple
from configs.constant import CONTENT, ROL<PERSON>, ASSISTANT, SYSTEM, USER, MAX_HISTORY_RUN
from models.prompts.system_prompt import DEFAULT_SYSTEM_MESSAGE


def convert_text_to_message(text: str, role: str = ASSISTANT) -> Dict:
    return {ROLE: role, CONTENT: text}


def convert_text_to_messages(text: str, system_message: str = DEFAULT_SYSTEM_MESSAGE):
    return [
        {ROLE: SYSTEM, CONTENT: system_message},
        {ROLE: USER, CONTENT: text}
    ]


def combine_history_messages(
        history: List, text: str, role: str = USER,
        system_message: str = None
):
    if history:
        messages = history[-MAX_HISTORY_RUN:] + [convert_text_to_message(text=text, role=role)]
    else:
        messages = convert_text_to_messages(text=text)
    system_message = system_message if system_message else DEFAULT_SYSTEM_MESSAGE
    if messages[0][ROL<PERSON>] != SYSTEM:
        messages = [convert_text_to_message(text=system_message, role=SYSTEM)] + messages
    else:
        messages[0][CONTENT] = system_message
    return messages


def clean_think(text: str):
    cleaned_text = re.sub(r'<think>.*?</think>', '', text, flags=re.DOTALL)
    return cleaned_text


def extract_python_code(text: str) -> List[Tuple[str, str]]:
    """
    从文本中提取Python代码块，支持转义字符处理

    Args:
        text (str): 包含代码块的文本

    Returns:
        List[Tuple[str, str]]: 返回(语言标识, 代码内容)的元组列表
    """
    # 匹配代码块的正则表达式
    # 支持 ```python, ```py, ``` 或直接的代码块
    text = clean_think(text)
    code_block_pattern = r'```(?:python|py)?\s*\n(.*?)\n```'

    # 使用DOTALL标志让.匹配换行符
    matches = re.findall(code_block_pattern, text, re.DOTALL)

    result = []
    for code in matches:
        # 去除首尾空白
        cleaned_code = code.strip()

        # 处理转义字符
        cleaned_code = process_escape_characters(cleaned_code)

        if cleaned_code:
            result.append(("python", cleaned_code))

    return result


def process_escape_characters(code: str) -> str:
    """
    处理代码中的转义字符

    Args:
        code (str): 原始代码字符串

    Returns:
        str: 处理后的代码字符串
    """
    # 处理常见的转义字符
    processed_code = code.replace('\\n', '\n')  # 换行符
    processed_code = processed_code.replace('\\"', '"')  # 双引号
    processed_code = processed_code.replace("\\'", "'")  # 单引号
    processed_code = processed_code.replace('\\t', '\t')  # 制表符
    processed_code = processed_code.replace('\\\\', '\\')  # 反斜杠

    # 去掉开头的多余换行符
    processed_code = processed_code.lstrip('\n')

    return processed_code


def extract_class_names_regex(code: str) -> List[str]:
    """
    使用正则表达式提取Python代码中的类名

    Args:
        code (str): Python代码字符串

    Returns:
        List[str]: 类名列表
    """
    # 正则表达式匹配类定义
    pattern = r'^class\s+([A-Za-z_][A-Za-z0-9_]*)\s*[\(:]'
    matches = re.findall(pattern, code, re.MULTILINE)
    return matches


def extract_all_code_blocks(text: str) -> List[Tuple[str, str]]:
    """
    提取所有代码块，包括不同语言的

    Args:
        text (str): 包含代码块的文本

    Returns:
        List[Tuple[str, str]]: 返回(语言标识, 代码内容)的元组列表
    """
    # 更通用的代码块匹配模式
    code_block_pattern = r'```(\w+)?\s*\n(.*?)\n```'

    matches = re.findall(code_block_pattern, text, re.DOTALL)

    result = []
    for lang, code in matches:
        cleaned_code = code.strip()
        if cleaned_code:
            # 如果没有指定语言，尝试根据代码内容判断
            if not lang:
                lang = detect_language(cleaned_code)
            result.append((lang or "unknown", cleaned_code))

    return result


def detect_language(code: str) -> str:
    """
    简单的代码语言检测

    Args:
        code (str): 代码内容

    Returns:
        str: 检测到的语言
    """
    # 简单的关键词检测
    python_keywords = ['def ', 'import ', 'from ', 'class ', 'if __name__']
    js_keywords = ['function ', 'const ', 'let ', 'var ', '=>']

    code_lower = code.lower()

    python_count = sum(1 for keyword in python_keywords if keyword in code_lower)
    js_count = sum(1 for keyword in js_keywords if keyword in code_lower)

    if python_count > js_count:
        return "python"
    elif js_count > 0:
        return "javascript"
    else:
        return "unknown"


def save_code_to_file(code: str, filename: str = "extracted_code.py"):
    """
    将提取的代码保存到文件

    Args:
        code (str): 代码内容
        filename (str): 保存的文件名
    """
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(code)
    print(f"代码已保存到: {filename}")


# 使用示例
def main():
    # 从文件读取内容的快速函数
    def extract_from_file(filename: str) -> str:
        """从文件中快速提取第一个Python代码块"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                content = f.read()

            codes = extract_python_code(content)
            if codes:
                return codes[0][1]  # 返回第一个代码块的内容
            else:
                return "未找到Python代码块"
        except FileNotFoundError:
            return f"文件 {filename} 不存在"
        except Exception as e:
            return f"读取文件时出错: {e}"

    # 测试示例
    sample_text = '''
这是一些文本内容

```python
from manim import *
import numpy as np

def hello():
    print("Hello World")
```

还有一些其他内容

```
def another_function():
    return "test"
```

```javascript
function test() {
    console.log("JS code");
}
```
'''

    # 提取Python代码
    python_codes = extract_python_code(sample_text)
    print("提取的Python代码块:")
    for i, (lang, code) in enumerate(python_codes, 1):
        print(f"\n=== 代码块 {i} ({lang}) ===")
        print(code)

    # 提取所有代码块
    all_codes = extract_all_code_blocks(sample_text)
    print("\n\n所有代码块:")
    for i, (lang, code) in enumerate(all_codes, 1):
        print(f"\n=== 代码块 {i} ({lang}) ===")
        print(code[:100] + "..." if len(code) > 100 else code)

    # 演示从文件提取
    print("\n=== 从文件提取示例 ===")
    # extracted_code = extract_from_file("paste.txt")
    # print("提取结果:", extracted_code[:200] + "..." if len(extracted_code) > 200 else extracted_code)


if __name__ == "__main__":
    main()
