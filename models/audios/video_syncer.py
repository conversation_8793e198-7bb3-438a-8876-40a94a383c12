import os

from moviepy import VideoFileClip, AudioFileClip, CompositeAudioClip

from models.types.types import VideoScript


class VideoSyncer:
    """音视频同步处理器"""

    def __init__(self, video_file: str):
        self.video_file = video_file

        if not os.path.exists(video_file):
            raise FileNotFoundError(f"视频文件不存在: {video_file}")

    def create_synced_video(self, scripts: VideoScript, output_file: str = "math_lesson_final.mp4") -> str:
        """创建同步的教学视频"""

        print("🎵 开始同步音视频...")

        try:
            # 加载视频
            video = VideoFileClip(self.video_file)
            print(f"  ✓ 视频时长: {video.duration:.2f}秒")

            # 收集音频片段
            audio_clips = []
            start_time = 0
            for segment in scripts.video_segment:
                audio_file = segment.audio_file

                if os.path.exists(audio_file):
                    try:
                        audio_clip = AudioFileClip(audio_file)
                        # 检查音频文件是否有效
                        if audio_clip.duration <= 0:
                            print(f"  ⚠️ 音频文件 {audio_file} 时长为0")
                            continue

                        # 设置音频开始时间
                        audio_clip = audio_clip.with_start(start_time)
                        start_time += float(segment.duration.replace("秒", ''))
                        audio_clips.append(audio_clip)
                        print(f"  ✓ 添加音频: (时长: {audio_clip.duration:.2f}s)")
                    except Exception as e:
                        print(f"  ⚠️ 音频文件 {audio_file} 加载失败: {e}")
                else:
                    print(f"  ⚠️ 音频文件不存在: {audio_file}")
            audio_file = scripts.summary.audio_file
            audio_clip = AudioFileClip(audio_file)
            audio_clip = audio_clip.with_start(start_time)
            audio_clips.append(audio_clip)
            if not audio_clips:
                print("  ❌ 没有找到有效的音频文件")
                return None

            # 合并音频
            final_audio = CompositeAudioClip(audio_clips)
            print(f"  ✓ 合并后音频时长: {final_audio.duration:.2f}秒")

            # 调整视频长度匹配音频（如果需要）
            if final_audio.duration > video.duration:
                print(f"  ⚠️ 音频比视频长，视频将延长到匹配音频时长")
                # 可以选择：1) 截断音频 2) 延长视频 3) 报错
                # 这里选择截断音频
                final_audio = final_audio.subclipped(0, video.duration)
            elif final_audio.duration < video.duration:
                print(f"  ℹ️ 视频比音频长，视频将被截断到匹配音频时长")
                video = video.subclipped(0, final_audio.duration)

            # 合并视频和音频
            final_video = video.with_audio(final_audio)

            # 导出最终视频
            print(f"  🎬 正在导出视频: {output_file}")
            final_video.write_videofile(
                output_file,
                codec='libx264',
                audio_codec='aac',
                temp_audiofile='temp-audio.m4a',
                remove_temp=True,
                logger=None
            )

            # 清理资源
            video.close()
            final_audio.close()
            final_video.close()

            print(f"✅ 教学视频生成完成: {output_file}")
            return output_file

        except Exception as e:
            print(f"❌ 音视频同步失败: {e}")
            return None
