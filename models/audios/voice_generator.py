import contextlib
import math
import subprocess
import tempfile
import wave
from copy import deepcopy
import json, os

import json_repair
from moviepy import Audio<PERSON><PERSON>Clip

from configs.constant import ASSISTANT, LLM_DATA_KIMI_DATA, QWEN_PLUS_LLM_MODEL_DATA, LLM_DATA_CLAUDE_DATA
from models.audios.text_to_speech import openai_text_to_speech
from models.llms.llm import llm_chat_v2
from models.logger import logger
from models.prompts.generate_manim_video import GENERATE_MATH_PROMPT
from models.prompts.generate_video_script import GENERATE_VIDEO_SCRIPT_PROMPT
from models.utils import (
    convert_text_to_messages, combine_history_messages, convert_text_to_message,
    extract_all_code_blocks, extract_class_names_regex
)
from models.types.types import VideoScript


class VoiceGenerator:
    """AI语音内容生成器 - 音频优先模式"""

    def __init__(self):
        self.scripts: VideoScript = None
        self.audio_durations = {}

    async def generate_lesson_script(self):
        prompt = GENERATE_MATH_PROMPT + f"\n数学教学文案{self.scripts.to_dict()}"
        messages = convert_text_to_messages(prompt)
        history = deepcopy(messages)
        llm_data = LLM_DATA_CLAUDE_DATA

        while True:
            try:
                chat_stream = await llm_chat_v2(messages, llm_data, stream=False)
                content = chat_stream.choices[0].message.content
                codes = extract_all_code_blocks(content)
                logger.info(f"{codes}")
                if len(codes) == 1 and codes[0][0] == 'python' and len(extract_class_names_regex(codes[0][1])) == 1:
                    code = codes[0][1]
                    class_name = extract_class_names_regex(codes[0][1])[0]
                    result, is_success, error_msg = await self.generate_adaptive_manim_video(
                        code, lesson_name=class_name
                    )

                    if is_success:
                        logger.info("生成成功!!!")
                        return result, class_name
                    else:
                        logger.warning(f"{code}当前生成python脚本不符合需求: {error_msg}")
                        history += [convert_text_to_message(text=content, role=ASSISTANT)]
                        text = f"当前生成python脚本不符合需求，当前报错: {error_msg}"
                        messages = combine_history_messages(history, text=text)
                else:
                    logger.warning("生成的代码格式不符合要求，重新生成...")
                    history += [convert_text_to_message(text=content, role=ASSISTANT)]
                    text = f"{codes}生成的代码格式不符合要求，请确保生成单个Python类且包含在代码块中"
                    messages = combine_history_messages(history, text=text)

            except Exception as e:
                logger.error(f"生成过程中出现异常: {str(e)}")
                # 可以选择继续重试或者抛出异常
                history += [convert_text_to_message(text="出现异常，请重新生成", role=ASSISTANT)]
                text = f"生成过程中出现异常: {str(e)}，请重新生成"
                messages = combine_history_messages(history, text=text)

    async def generate_adaptive_manim_video(self, code: str, lesson_name: str):
        """生成自适应时长的manim视频"""
        temp_path = None

        try:
            # 创建临时文件
            with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False, encoding='utf-8') as tmp:
                tmp.write(code)
                temp_path = tmp.name

            print(f"临时文件路径: {temp_path}")

            # 构建命令
            cmd = [
                'manim', '-pql',  # -p预览 -q低质量（快速） -l480p
                temp_path,  # 当前文件
                lesson_name  # 场景类名
            ]

            # 使用subprocess运行命令，捕获输出和错误
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=300,  # 5分钟超时
                cwd=os.getcwd()
            )

            if result.returncode == 0:
                logger.info(f"Manim视频生成成功: {temp_path}")
                return temp_path, True, None
            else:
                error_msg = f"Manim执行失败 (退出码: {result.returncode})\n"
                error_msg += f"标准输出: {result.stdout}\n" if result.stdout else ""
                error_msg += f"错误输出: {result.stderr}" if result.stderr else ""
                logger.error(error_msg)
                return temp_path, False, error_msg

        except subprocess.TimeoutExpired:
            error_msg = "Manim执行超时(超过5分钟)"
            logger.error(error_msg)
            return temp_path, False, error_msg

        except FileNotFoundError:
            error_msg = "找不到manim命令，请确保已正确安装manim"
            logger.error(error_msg)
            return temp_path, False, error_msg

        except PermissionError as e:
            error_msg = f"权限错误: {str(e)}"
            logger.error(error_msg)
            return temp_path, False, error_msg

        except Exception as e:
            error_msg = f"生成视频时出现未知错误: {str(e)}"
            logger.error(error_msg)
            return temp_path, False, error_msg

    async def generate_scripts(self, query: str):
        """生成讲解词脚本"""
        llm_data = LLM_DATA_KIMI_DATA
        prompt = GENERATE_VIDEO_SCRIPT_PROMPT + f"\n用户的问题：{query}"
        messages = convert_text_to_messages(prompt)
        history = deepcopy(messages)
        while True:
            chat_stream = await llm_chat_v2(messages, llm_data, stream=False)
            content = chat_stream.choices[0].message.content
            scripts = json_repair.loads(content)
            if isinstance(scripts, list):
                scripts = [s for s in scripts if s][0]
            try:
                self.scripts = VideoScript.from_dict(scripts)
                break
            except Exception as e:
                logger.warning(f"当前生成文案不符合需求,{e},{scripts}")
                history += [convert_text_to_message(text=content, role=ASSISTANT)]
                text = f"你生成的内容不符合解析需求{e}"
                messages = combine_history_messages(history, text=text)

        # 保存脚本
        with open("scripts.json", "w", encoding="utf-8") as f:
            json.dump(self.scripts.to_dict(), f, ensure_ascii=False, indent=2)
        print("✅ 讲解词脚本生成完成！")

    async def text_to_speech(self):
        """将文字转换为语音文件，返回每个音频的实际时长"""

        print("🎙️ 开始生成语音文件...")
        # video_segment
        for segment in self.scripts.video_segment:
            text = segment.voice_over
            try:
                # 生成语音
                audio_file = f"audio_{segment.segment_title}.wav"
                segment.audio_file = audio_file
                await openai_text_to_speech(output_file_path=audio_file, text=text)
                # 测量音频时长
                duration = self.get_audio_duration(audio_file)
                segment.duration = f"{duration}秒"
                print(f"  ✓ {segment.segment_title}: {audio_file} (时长: {duration:.2f}秒)")

            except Exception as e:
                print(f"  ❌ 生成 {segment.segment_title} 语音失败: {e}")
                return {}
        audio_file = f"audio_{self.scripts.summary.title}.wav"
        await openai_text_to_speech(output_file_path=audio_file, text=self.scripts.summary.voice_over)
        self.scripts.summary.audio_file = audio_file

    @staticmethod
    def get_audio_duration(audio_file: str) -> float:
        """获取音频文件的时长"""
        try:
            with contextlib.closing(wave.open(audio_file, 'r')) as f:
                frames = f.getnframes()
                rate = f.getframerate()
                duration = frames / float(rate)
                return duration
        except Exception as e:
            print(f"  ⚠️ 无法获取 {audio_file} 时长: {e}")
            # 备用方法，使用moviepy
            try:
                audio_clip = AudioFileClip(audio_file)
                duration = audio_clip.duration
                audio_clip.close()
                return duration
            except:
                return 3.0  # 默认3秒
