from openai import AsyncOpenAI

api_key = 'gpustack_28ff806c814cca91_da358c944d35721136249308a0df0b5e'
client = AsyncOpenAI(
    base_url="http://192.168.99.24:8080/v1",
    api_key=api_key
)


async def openai_text_to_speech(output_file_path, text):
    response = await client.audio.speech.create(
        model="cosyvoice-300m",
        voice="Chinese Female",
        response_format="wav",
        input=text,
    )

    with open(output_file_path, "wb") as f:
        for chunk in response.iter_bytes():
            f.write(chunk)

    print(f"Audio saved to {output_file_path}")
