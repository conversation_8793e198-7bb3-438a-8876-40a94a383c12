import os
from pathlib import Path
from typing import Optional

from models.audios.video_syncer import VideoSyncer
from models.audios.voice_generator import VoiceGenerator


class AudioFirstLessonCreator:
    """教学视频创建器主控制器 - 音频优先模式"""

    def __init__(self, output_dir: str = "output"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)

    async def create_complete_lesson(self, query: str):
        """创建完整的教学视频 - 音频优先流程"""

        print("🚀 开始制作AI配音教学视频（音频优先模式）")
        print("=" * 60)

        try:
            # 步骤1：生成AI语音并获取时长
            print("🎙️ 第1步：生成AI配音并测量时长...")
            voice_gen = VoiceGenerator()
            await voice_gen.generate_scripts(query)
            await voice_gen.text_to_speech()
            temp_path, class_name = await voice_gen.generate_lesson_script()
            # temp_path = await self.generate_adaptive_manim_video(code, class_name)
            # 步骤3：查找生成的视频文件
            video_file = self.find_video_file(temp_path, lesson_name=class_name)
            if not video_file:
                print("❌ 找不到生成的视频文件")
                return False

            # 步骤4：同步音视频（应该完美匹配）
            print("🎵 第3步：同步音视频...")
            syncer = VideoSyncer(video_file)
            output_file = syncer.create_synced_video(
                voice_gen.scripts, str(self.output_dir / f"{class_name}.mp4")
            )

            if output_file:
                print("=" * 60)
                print("🎉 教学视频制作完成！")
                print(f"📁 输出文件: {output_file}")
                print("📚 现在可以给小朋友播放啦！")
                print("\n🎯 音频优先模式的优势:")
                print("  • 语音自然流畅，不被强制压缩")
                print("  • 动画节奏完美匹配语音")
                print("  • 每个概念都有充足的解释时间")
                # self.cleanup_temp_files()
                return True
            else:
                return False

        except Exception as e:
            print(f"❌ 制作过程中出错: {e}")
            import traceback
            traceback.print_exc()
            return False

    # async def generate_adaptive_manim_video(self, code: str, lesson_name: str):
    #     """生成自适应时长的manim视频"""
    #     with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False, encoding='utf-8') as tmp:
    #         tmp.write(code)  # 修正：移除错误的 bf 前缀
    #         temp_path = tmp.name
    #         cmd = [
    #             'manim', '-pql',  # -p预览 -q低质量（快速） -l480p
    #             temp_path,  # 当前文件
    #             lesson_name  # 场景类名
    #         ]
    #         try:
    #             result = subprocess.run(cmd, cwd=os.getcwd())
    #             return temp_path
    #         except Exception as e:
    #             return str(e)

    def find_video_file(self, temp_path: str, lesson_name: str) -> Optional[str]:
        """查找生成的视频文件"""
        # Manim默认输出路径
        video_name = os.path.basename(temp_path).replace(".py", '')
        possible_paths = [
            f"media/videos/{video_name}/480p15/{lesson_name}.mp4",
            f"media/videos/{video_name}/720p30/{lesson_name}.mp4",
            f"media/videos/{video_name}/1080p60/{lesson_name}.mp4",
        ]

        for path in possible_paths:
            if os.path.exists(path):
                print(f"  ✅ 找到视频文件: {path}")
                return path

        return None

    def cleanup_temp_files(self):
        """清理临时文件"""
        temp_files = [
            "timeline.json",
            "scripts.json",
            "audio_durations.json",
            "temp_audio_durations.json"
        ]

        # 清理音频文件
        for file in os.listdir("."):
            if file.startswith("audio_") and file.endswith(".wav"):
                temp_files.append(file)

        for file in temp_files:
            try:
                if os.path.exists(file):
                    os.remove(file)
                    print(f"  🗑️ 清理临时文件: {file}")
            except:
                pass
