import logging
from datetime import datetime, timedelta, timezone


class BeijingFormatter(logging.Formatter):
    # ANSI 颜色代码
    COLOR_CODES = {
        'DEBUG': '\033[36m',  # 青色
        'INFO': '\033[32m',  # 绿色
        'WARNING': '\033[33m',  # 黄色
        'ERROR': '\033[31m',  # 红色
        'CRITICAL': '\033[1;31m'  # 亮红色
    }
    RESET_CODE = '\033[0m'  # 重置颜色

    def formatTime(self, record, date_fmt=None):
        # Convert UTC time to Beijing time (UTC+8)
        dt = datetime.fromtimestamp(record.created, tz=timezone.utc) + timedelta(hours=8)
        if date_fmt:
            s = dt.strftime(date_fmt)
        else:
            s = dt.strftime('%Y-%m-%d %H:%M:%S')
        return s

    def format(self, record):
        # 获取对应日志级别的颜色
        color = self.COLOR_CODES.get(record.levelname, '')
        # 格式化日志消息
        message = super().format(record)
        # 添加颜色（只对日志级别和消息着色）
        if color:
            # 只对消息部分着色
            parts = message.split(' - ')
            if len(parts) >= 5:
                # 重新组合，只对最后两部分（levelname和message）着色
                parts[-2] = f"{color}{parts[-2]}{self.RESET_CODE}"
                parts[-1] = f"{color}{parts[-1]}{self.RESET_CODE}"
                message = ' - '.join(parts)
        return message


def setup_logger(level=logging.INFO):
    _logger = logging.getLogger('fire_logger')
    _logger.setLevel(level)
    _logger.propagate = False
    # 检查是否已经有处理器
    if not _logger.handlers:
        handler = logging.StreamHandler()
        handler.setLevel(level)
        formatter = BeijingFormatter(
            '%(asctime)s - %(filename)s - %(lineno)d - %(levelname)s - %(message)s'
        )
        handler.setFormatter(formatter)
        _logger.addHandler(handler)
    return _logger


logger = setup_logger()

# 测试代码
if __name__ == '__main__':
    logger.debug("这是一条调试信息")
    logger.info("这是一条普通信息")
    logger.warning("这是一条警告信息")
    logger.error("这是一条错误信息")
    logger.critical("这是一条严重错误信息")
